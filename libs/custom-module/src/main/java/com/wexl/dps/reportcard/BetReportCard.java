package com.wexl.dps.reportcard;

import static com.wexl.retail.content.model.QuestionType.SPCH;

import com.wexl.admissiontests.repository.AdmissionTestRepository;
import com.wexl.dps.dto.BetProficiency;
import com.wexl.dps.dto.BetReportDto;
import com.wexl.dps.repository.BetExamDetailsRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.ReportCardTemplateRepository;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.proctoring.repository.ProctoringRepository;
import com.wexl.retail.proctoring.service.ProctoringService;
import com.wexl.retail.speech.SpeechService;
import com.wexl.retail.speech.dto.SpeechEvaluation;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestRepository;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestDefinitionSection;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import com.wexl.retail.test.school.repository.TestQuestionRepository;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.QRCodeGenerator;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class BetReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final StudentAuthService studentAuthService;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final ScheduleTestRepository scheduleTestRepository;
  private final ExamRepository examRepository;
  private final UserService userService;
  private final List<SpeechService> speechServices;
  private final TestDefinitionService testDefinitionService;
  private final StorageService storageService;
  private final TestDefinitionRepository testDefinitionRepository;
  private final ScheduleTestService scheduleTestService;
  private final AdmissionTestRepository admissionTestRepository;
  private final QRCodeGenerator qrCodeGenerator;
  private final ProctoringRepository proctoringRepository;
  private final ProctoringService proctoringService;
  private static final String IMPROMPTU_SPEECH = "Impromptu Speech";
  private final ReportCardTemplateRepository reportCardTemplateRepository;
  private final TestQuestionRepository testQuestionRepository;
  private final BetExamDetailsRepository betExamDetailsRepository;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var student = studentAuthService.validateStudentByUser(user);
    var scheduleTest =
        scheduleTestRepository
            .findById(request.offlineTestDefinitionId())
            .orElseThrow(() -> new ApiException(InternalErrorCodes.INVALID_REQUEST, ""));
    var header = constructHeader(student, org, scheduleTest);
    var body = buildBody(student, scheduleTest, request.templateId());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  public BetReportDto.Body buildBody(Student student, ScheduleTest scheduleTest, Long templateId) {

    var scheduleTestStudent =
        scheduleTestStudentRepository
            .findByScheduleTestAndStudent(scheduleTest, student.getUserInfo())
            .orElseThrow();
    if (!TestStudentStatus.COMPLETED.name().equals(scheduleTestStudent.getStatus())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.downloadReportCard");
    }

    var testDefinition = scheduleTestStudent.getScheduleTest().getTestDefinition();

    var testDefinitionSections =
        testDefinition.getTestDefinitionSections().stream()
            .sorted(Comparator.comparing(TestDefinitionSection::getSequenceNumber))
            .toList();

    var exams =
        examRepository.findByScheduleTestAndStudentIn(
            scheduleTestStudent.getScheduleTest(), List.of(student));
    if (exams.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Exam not found");
    }
    var exam = exams.stream().max(Comparator.comparing(Exam::getCreatedAt)).orElseThrow();

    Map<String, ExamAnswer> resultMap =
        exam.getExamAnswers().stream()
            .collect(Collectors.toMap(ExamAnswer::getQuestionUuid, ea -> ea));

    var testDefinitionSectionResult =
        testDefinitionSections.stream()
            .map(
                tds -> {
                  var testQuestions = testQuestionRepository.findByTestDefinitionSection(tds);
                  var examAnswers =
                      testQuestions.stream()
                          .map(testQuestion -> resultMap.get(testQuestion.getQuestionUuid()))
                          .toList();
                  var marksScored =
                      examAnswers.stream().mapToDouble(ExamAnswer::getMarksScoredPerQuestion).sum();
                  var totalMarks =
                      examAnswers.stream().mapToDouble(ExamAnswer::getMarksPerQuestion).sum();
                  var totalQuestions = tds.getNoOfQuestions();

                  return QuestionDto.TestDefinitionSection.builder()
                      .name(tds.getName())
                      .marks(marksScored / totalMarks)
                      .totalQuestions(totalQuestions)
                      .build();
                })
            .toList();
    BetReportDto.PronunciationAssessment speechAnalysis = buildSpeechAnalysis(testDefinition, exam);
    var overallIeltsScore = speechAnalysis.ieltsScore();

    String sect1Name = getSectionName(testDefinitionSectionResult, 0);
    String sect2Name = getSectionName(testDefinitionSectionResult, 1);
    String sect3Name = getSectionName(testDefinitionSectionResult, 2);
    String sect4Name = getSectionName(testDefinitionSectionResult, 3);

    var section1Grade = getSectionGrade(testDefinitionSectionResult, 0);
    var section2Grade = getSectionGrade(testDefinitionSectionResult, 1);
    var section3Grade = getSectionGrade(testDefinitionSectionResult, 2);
    var section4Grade = getSectionGrade(testDefinitionSectionResult, 3);

    var section1TotalQuestions = getTotalQuestions(testDefinitionSectionResult, 0);
    var section2TotalQuestions = getTotalQuestions(testDefinitionSectionResult, 1);
    var section3TotalQuestions = getTotalQuestions(testDefinitionSectionResult, 2);
    var section4TotalQuestions = getTotalQuestions(testDefinitionSectionResult, 3);

    if ("speaking".equalsIgnoreCase(sect1Name)) {
      section1Grade = overallIeltsScore;
    } else if ("speaking".equalsIgnoreCase(sect2Name)) {
      section2Grade = overallIeltsScore;
    } else if ("speaking".equalsIgnoreCase(sect3Name)) {
      section3Grade = overallIeltsScore;
    } else if ("speaking".equalsIgnoreCase(sect4Name)) {
      section4Grade = overallIeltsScore;
    }

    var score = sumScore(section1Grade, section2Grade, section3Grade, section4Grade);
    var overallScore = scheduleTestService.getOverallScore(score);
    var testDefinitions =
        testDefinitionRepository
            .findTop100ByGradeSlugAndOrganizationAndTypeAndDeletedAtIsNullOrderByCreatedAtDesc(
                testDefinition.getGradeSlug(),
                testDefinition.getOrganization(),
                TestType.MOCK_TEST);

    Boolean corporateBet = false;
    if (templateId != null) {
      var reportTemplateConfig =
          reportCardTemplateRepository.findById(templateId).get().getConfig();
      corporateBet = reportTemplateConfig.equals("inter-bet-report-card.xml");
    }
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    String formattedDate = exam.getEndTime().toLocalDateTime().toLocalDate().format(formatter);

    var response =
        BetReportDto.Body.builder()
            .examDate(formattedDate)
            .sectionListSize(String.valueOf(testDefinitionSections.size()))
            .testLevel(findTestLevel(testDefinitions, testDefinition))
            .section1Name(sect1Name)
            .section2Name(sect2Name)
            .section3Name(sect3Name)
            .section4Name(sect4Name)
            .section1Grade(
                massageScore(section1Grade) != null && massageScore(section1Grade).equals("0")
                    ? " "
                    : getProficiency(section1Grade, corporateBet).replace("_", "-"))
            .section2Grade(
                massageScore(section2Grade) != null && massageScore(section2Grade).equals("0")
                    ? " "
                    : getProficiency(section2Grade, corporateBet).replace("_", "-"))
            .section3Grade(
                massageScore(section3Grade) != null && massageScore(section3Grade).equals("0")
                    ? " "
                    : getProficiency(section3Grade, corporateBet).replace("_", "-"))
            .section4Grade(
                massageScore(section4Grade) != null && massageScore(section4Grade).equals("0")
                    ? " "
                    : getProficiency(section4Grade, corporateBet).replace("_", "-"))
            .section1Value(
                "speaking".equalsIgnoreCase(sect1Name)
                    ? String.valueOf(overallIeltsScore)
                    : massageScore(section1Grade))
            .section2Value(
                "speaking".equalsIgnoreCase(sect2Name)
                    ? String.valueOf(overallIeltsScore)
                    : massageScore(section2Grade))
            .section3Value(
                "speaking".equalsIgnoreCase(sect3Name)
                    ? String.valueOf(overallIeltsScore)
                    : massageScore(section3Grade))
            .section4Value(
                "speaking".equalsIgnoreCase(sect4Name)
                    ? String.valueOf(overallIeltsScore)
                    : massageScore(section4Grade))
            .proficiencyValue(getProficiency(overallScore, corporateBet).replace("_", "-"))
            .scoreValue(overallScore)
            .speechAnalysis(speechAnalysis)
            .Section1Questions(section1TotalQuestions)
            .Section2Questions(section2TotalQuestions)
            .Section3Questions(section3TotalQuestions)
            .Section4Questions(section4TotalQuestions)
            .build();
    buildBetExamDetails(student.getUserInfo().getOrganization(), response, exam);
    return response;
  }

  private void buildBetExamDetails(String orgSlug, BetReportDto.Body response, Exam exam) {
    BetExamDetails examDetail = new BetExamDetails();
    examDetail.setExamId(exam.getId());
    examDetail.setOverallScore(response.scoreValue());
    examDetail.setProficiencyValue(response.proficiencyValue());
    examDetail.setExamDate(response.examDate());
    examDetail.setOrgSlug(orgSlug);
    examDetail.setSpeechAnalysis(response.speechAnalysis());
    examDetail.setTestLevel(response.testLevel());
    examDetail.setBetExamSectionDetails(buildBetExamSectionDetails(response, examDetail));
    betExamDetailsRepository.save(examDetail);
  }

  private List<BetExamSectionDetails> buildBetExamSectionDetails(
      BetReportDto.Body response, BetExamDetails examDetail) {
    List<BetExamSectionDetails> sectionDetails = new ArrayList<>();
    sectionDetails.add(
        BetExamSectionDetails.builder()
            .betExamDetails(examDetail)
            .sectionName(response.section1Name())
            .sectionGrade(response.section1Grade())
            .sectionValue(response.section1Value())
            .questionCount(response.Section1Questions())
            .build());
    sectionDetails.add(
        BetExamSectionDetails.builder()
            .betExamDetails(examDetail)
            .sectionName(response.section2Name())
            .sectionGrade(response.section2Grade())
            .sectionValue(response.section2Value())
            .questionCount(response.Section2Questions())
            .build());
    sectionDetails.add(
        BetExamSectionDetails.builder()
            .betExamDetails(examDetail)
            .sectionName(response.section3Name())
            .sectionGrade(response.section3Grade())
            .sectionValue(response.section3Value())
            .questionCount(response.Section3Questions())
            .build());
    sectionDetails.add(
        BetExamSectionDetails.builder()
            .betExamDetails(examDetail)
            .sectionName(response.section4Name())
            .sectionGrade(response.section4Grade())
            .sectionValue(response.section4Value())
            .questionCount(response.Section4Questions())
            .build());
    return sectionDetails;
  }

  private String findTestLevel(
      List<TestDefinition> testDefinitions, TestDefinition testDefinition) {
    int index = testDefinitions.indexOf(testDefinition);
    if (index == 0) return "INITIAL";
    return "FINAL";
  }

  private BetReportDto.PronunciationAssessment buildSpeechAnalysis(
      TestDefinition testDefinition, Exam exam) {
    var questionResponse =
        testDefinitionService.getQuestionResponsePreconfigured(testDefinition, 0);
    var questionMap =
        questionResponse.testDefinitionSectionResponses().stream()
            .map(QuestionDto.TestDefinitionSectionResponse::questions)
            .flatMap(Collection::stream)
            .collect(Collectors.groupingBy(QuestionDto.Question::uuid));

    var speechExamAnswers =
        exam.getExamAnswers().stream()
            .filter(
                q ->
                    SPCH.getType().equals(q.getType())
                        && Objects.nonNull(q.getSpchSelectedAnswer()))
            .toList();

    var speechResponses =
        speechExamAnswers.stream()
            .map(
                sea -> {
                  var question = questionMap.get(sea.getQuestionUuid());
                  try {
                    var speechResponse =
                        speechServices
                            .getFirst()
                            .pronunciationAssessment(String.valueOf(sea.getId()));
                    if (Objects.nonNull(speechResponse)) {
                      return speechResponse;
                    }
                    return speechServices
                        .getFirst()
                        .pronunciationAssessment(
                            massage(question.getFirst().question()),
                            sea.getSpchSelectedAnswer(),
                            String.valueOf(sea.getId()),
                            question.getFirst().category().equals(IMPROMPTU_SPEECH));
                  } catch (Exception e) {
                    log.error("Failed to evaluate the speech analysis : {}", e.getMessage(), e);
                    return null;
                  }
                })
            .filter(Objects::nonNull)
            .toList();

    var assessments =
        speechResponses.stream()
            .map(SpeechEvaluation.SpeechResponse::assessment)
            .filter(Objects::nonNull)
            .toList();
    var accuracyAvg =
        assessments.stream()
            .mapToDouble(SpeechEvaluation.PronunciationAssessment::accuracyScore)
            .average();
    var pronunciationAvg =
        assessments.stream()
            .mapToDouble(SpeechEvaluation.PronunciationAssessment::pronunciationScore)
            .average();
    var ieltsAvg =
        assessments.stream()
            .mapToDouble(SpeechEvaluation.PronunciationAssessment::ieltsScore)
            .average();
    var ieltsFinalScore = scheduleTestService.getOverallScore(ieltsAvg.orElse(0.0));
    var pteAvg =
        assessments.stream()
            .mapToDouble(SpeechEvaluation.PronunciationAssessment::pteScore)
            .average();
    var toeicAvg =
        assessments.stream()
            .mapToDouble(SpeechEvaluation.PronunciationAssessment::toeicScore)
            .average();
    var completenessAvg =
        assessments.stream()
            .mapToDouble(SpeechEvaluation.PronunciationAssessment::completenessScore)
            .average();
    var fluencyAvg =
        assessments.stream()
            .mapToDouble(SpeechEvaluation.PronunciationAssessment::fluencyScore)
            .average();
    var cefrScore = assessments.isEmpty() ? null : assessments.getFirst().cefrScore();

    return BetReportDto.PronunciationAssessment.builder()
        .accuracyScore(accuracyAvg.isPresent() ? Math.round((60 + accuracyAvg.getAsDouble())) : 60)
        .cefrScore(cefrScore)
        .pronunciationScore(
            pronunciationAvg.isPresent()
                ? Math.round(126 - (pronunciationAvg.getAsDouble() * 1.26))
                : 126)
        .ieltsScore(ieltsFinalScore)
        .pteScore(pteAvg.isPresent() ? Math.round(pteAvg.getAsDouble()) : null)
        .fluencyScore(pronunciationAvg.isPresent() ? Math.round(pronunciationAvg.getAsDouble()) : 0)
        .completenessScore(accuracyAvg.isPresent() ? Math.round(accuracyAvg.getAsDouble()) : 0)
        .toeicScore(toeicAvg.isPresent() ? Math.round(toeicAvg.getAsDouble()) : null)
        .build();
  }

  private String massageScore(Double score) {
    if (Objects.isNull(score)) return null;
    else if (score % 1 == 0) return String.valueOf(score.intValue());
    return String.valueOf(score);
  }

  public String massage(String question) {
    // this is a temporary hack till we fix the questions.
    String s = question.replace("<p>", "").replace("</p>", "");
    s = s.toLowerCase().replace("say the sentence", "");
    return s;
  }

  private Double getSectionGrade(
      List<QuestionDto.TestDefinitionSection> testDefinitionSectionResult, int index) {
    if (testDefinitionSectionResult.size() <= index) {
      return null;
    }
    return getBetScore(testDefinitionSectionResult.get(index).marks() * 100);
  }

  private String getSectionName(
      List<QuestionDto.TestDefinitionSection> testDefinitionSectionResult, int index) {
    if (testDefinitionSectionResult.size() <= index) {
      return null;
    }
    return testDefinitionSectionResult.get(index).name();
  }

  private Long getTotalQuestions(
      List<QuestionDto.TestDefinitionSection> testDefinitionSectionResult, int index) {
    if (testDefinitionSectionResult.size() <= index) {
      return null;
    }
    return testDefinitionSectionResult.get(index).totalQuestions();
  }

  private Double sumScore(Double... scores) {
    return Arrays.stream(scores)
        .filter(Objects::nonNull)
        .mapToDouble(Double::doubleValue)
        .average()
        .orElse(0.0);
  }

  private Double getBetScore(Double score) {
    if (Objects.isNull(score)) {
      return null;
    }
    if (score >= 96) return 9.0;
    else if (score >= 91) return 8.5;
    else if (score >= 86) return 8.0;
    else if (score >= 80) return 7.5;
    else if (score >= 75) return 7.0;
    else if (score >= 65) return 6.5;
    else if (score >= 55) return 6.0;
    else if (score >= 45) return 5.5;
    else if (score >= 35) return 5.0;
    else if (score >= 30) return 4.5;
    else if (score >= 25) return 4.0;
    else if (score >= 20) return 3.5;
    else if (score >= 17) return 3.0;
    else if (score >= 14) return 2.5;
    else if (score >= 10) return 2.0;
    else if (score >= 5) return 1.5;
    else if (score >= 0.1) return 1.0;
    else return 0.0;
  }

  private String getProficiency(Double score, Boolean corporateBet) {
    try {
      if (Objects.isNull(score)) {
        return BetProficiency.NOT_ATTEMPTED.name();
      }
      return switch (score.toString()) {
        case "9.0" -> BetProficiency.EXPERT.name();
        case "8.0", "8.5" ->
            corporateBet ? BetProficiency.PROFICIENT.name() : BetProficiency.ADVANCED.name();
        case "7.0", "7.5" ->
            corporateBet ? BetProficiency.ADVANCED.name() : BetProficiency.PROFICIENT.name();
        case "6.0", "6.5" -> BetProficiency.UPPER_INTERMEDIATE.name();
        case "5.0", "5.5" -> BetProficiency.INTERMEDIATE.name();
        case "4.0", "4.5" -> BetProficiency.LOW_INTERMEDIATE.name();
        case "3.0", "3.5" -> BetProficiency.BASIC.name();
        case "2.0", "2.5" -> BetProficiency.ELEMENTARY.name();
        case "1.0", "1.5" -> BetProficiency.BEGINNER.name();
        case "0.0", "0.5" -> BetProficiency.PREPARATION_NEEDED.name();
        default -> BetProficiency.NOT_ATTEMPTED.name();
      };
    } catch (Exception e) {
      return BetProficiency.NOT_ATTEMPTED.name();
    }
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    return List.of("bet-report-card.xml", "inter-bet-report-card.xml")
        .contains(reportCardTemplate.getConfig());
  }

  public BetReportDto.Header constructHeader(
      Student student, Organization organization, ScheduleTest scheduleTest) {
    var admissionTests = admissionTestRepository.findByTestScheduleId(scheduleTest.getId());
    var user = student.getUserInfo();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    Optional<StudentAttributeValueModel> nationality =
        reportCardService.getStudentAttributeValue(student, "nationality");
    Optional<StudentAttributeValueModel> programme =
        reportCardService.getStudentAttributeValue(student, "programme");

    Optional<StudentAttributeValueModel> motherTongue =
        reportCardService.getStudentAttributeValue(student, "mother_tongue");

    var motherGuardian =
        student.getGuardians().stream()
            .filter(guardian -> GuardianRole.MOTHER.equals(guardian.getRelationType()))
            .findFirst();
    var fatherGuardian =
        student.getGuardians().stream()
            .filter(guardian -> GuardianRole.FATHER.equals(guardian.getRelationType()))
            .findFirst();
    LocalDate today = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    String formattedDate = today.format(formatter);
    if (admissionTests == null) {
      return BetReportDto.Header.builder()
          .certificateId(null)
          .programme(programme.map(StudentAttributeValueModel::getValue).orElse(null))
          .motherTongue(motherTongue.map(StudentAttributeValueModel::getValue).orElse(null))
          .schoolName(organization.getName())
          .studentName(userService.getNameByUserInfo(user))
          .rollNo(getRollNumber(student.getClassRollNumber()))
          .motherName(
              motherGuardian
                  .map(
                      m ->
                          m.getFirstName() + " " + (m.getLastName() == null ? "" : m.getLastName()))
                  .orElse(null))
          .fatherName(
              fatherGuardian
                  .map(
                      m ->
                          m.getFirstName() + " " + (m.getLastName() == null ? "" : m.getLastName()))
                  .orElse(null))
          .countryOfRegion("INDIA")
          .studentId(getRollNumber(student.getRollNumber()))
          .sectionName(student.getSection().getName())
          .date(formattedDate)
          .orgSlug(organization.getSlug())
          .dob(
              dateOfBirth
                  .map(StudentAttributeValueModel::getValue)
                  .filter(dateStr -> dateStr != null && !dateStr.isBlank())
                  .map(
                      dateStr -> {
                        try {
                          return LocalDate.parse(dateStr, formatter).format(formatter);
                        } catch (DateTimeParseException e) {
                          return LocalDate.parse(dateStr).format(formatter);
                        }
                      })
                  .orElse(null))
          .gender(
              Objects.nonNull(user.getGender()) ? user.getGender().name().substring(0, 1) : null)
          .imageUrl(
              Objects.isNull(user.getProfileImage())
                  ? null
                  : storageService.generatePreSignedUrlForFetch(user.getProfileImage()))
          .nationality(nationality.map(StudentAttributeValueModel::getValue).orElse(null))
          .qrCodeUrl(getQrCodeLink(scheduleTest, student))
          .logo(organization.getLogo())
          .build();
    }

    return BetReportDto.Header.builder()
        .schoolName(organization.getName())
        .studentName(admissionTests.getName())
        .sectionName(admissionTests.getGradeSlug())
        .countryOfRegion("INDIA")
        .rollNo(admissionTests.getAdmissionNo())
        .studentId(admissionTests.getAdmissionNo())
        .date(formattedDate)
        .orgSlug(organization.getSlug())
        .isAdmissionTest(Objects.isNull(admissionTests) ? "false" : "true")
        .imageUrl(
            Objects.isNull(admissionTests.getProfileImage())
                ? null
                : storageService.generatePreSignedUrlForFetch(admissionTests.getProfileImage()))
        .qrCodeUrl(getQrCodeLink(scheduleTest, student))
        .logo(organization.getLogo())
        .build();
  }

  private String getQrCodeLink(ScheduleTest scheduleTest, Student student) {
    try {
      var exam = examRepository.getExamDetails(student.getId(), scheduleTest.getId()).orElseThrow();
      var qrCodePath = constructQrCodePath(student.getUserInfo().getOrganization(), exam.getRef());
      if (storageService.isFileAvailable(qrCodePath)) {
        return storageService.generatePreSignedUrlForFetch(qrCodePath);
      }
      var reportCardLink =
          constructBetReportCardLink(student.getUserInfo().getOrganization(), exam.getRef());
      var generateQRCode = qrCodeGenerator.generateQRCode(reportCardLink, 100, 100);
      storageService.uploadFile(generateQRCode, qrCodePath, MediaType.IMAGE_PNG_VALUE);
      var qrCodeLink = storageService.generatePreSignedUrlForFetch(qrCodePath);
      log.info("BET QR code link : {}", qrCodeLink);
      log.info("BET Report card link :{}", reportCardLink);
      return qrCodeLink;
    } catch (Exception e) {
      log.error("Failed to generate Bet report card QR code :{}", e.getMessage(), e);
      return null;
    }
  }

  private String constructQrCodePath(String orgSlug, String ref) {
    return String.format("%s/bet-qr/%s", orgSlug, ref);
  }

  private String constructBetReportCardLink(String orgSlug, String ref) {
    return "https://images.wexledu.com/%s"
        .formatted(String.format("%s/bet-report-card/%s", orgSlug, ref));
  }
}
