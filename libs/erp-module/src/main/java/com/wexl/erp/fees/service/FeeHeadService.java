package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.*;
import com.wexl.erp.fees.repository.FeeGroupFeeTypeRepository;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.erp.fees.repository.FeeMasterStudentRepository;
import com.wexl.erp.fees.service.fineType.FineTypeEngine;
import com.wexl.erp.fees.service.rules.RuleDto;
import com.wexl.erp.fees.service.rules.RuleParamType;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.util.ValidationUtils;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FeeHeadService {

  private final FeeGroupFeeTypeRepository feeGroupFeeTypeRepository;
  private final FeeHeadRepository feeHeadRepository;
  private final FeeMasterStudentRepository feeMasterStudentRepository;
  private final FeeRuleEngine feeRuleEngine;
  private final FineTypeEngine fineTypeEngine;
  private final FeeService feeService;
  private final ValidationUtils validationUtils;
  private final AuditTrailService auditTrailUtils;
  private final AuthService authService;

  public void createFeeHeads(UUID feeMasterId, FeeDto.FeeMasterRequest request, String orgSlug) {
    var feeGroup = feeService.getFeeGroupById(request.feeGroupId(), orgSlug);
    var feeMaster = feeService.getFeeMasterById(String.valueOf(feeMasterId), orgSlug);
    var students = feeRuleEngine.getStudentsForRule(buildRuleParam(request, feeMaster, orgSlug));
    var feeGroupFeeTypes =
        feeGroupFeeTypeRepository.findByFeeGroupIdAndOrgSlug(feeGroup.getId(), orgSlug);

    if (students.isEmpty()) {
      return;
    }

    students.forEach(
        student -> processStudentFeeHeads(student, feeMaster, feeGroupFeeTypes, orgSlug));

    if (ScopeType.CUSTOM.equals(request.scopeType())) {
      saveFeeMasterStudents(feeMaster, students);
    }
  }

  public void processStudentFeeHeads(
      Student student,
      FeeMaster feeMaster,
      List<FeeGroupFeeType> feeGroupFeeTypes,
      String orgSlug) {
    for (FeeGroupFeeType feeGroupFeeType : feeGroupFeeTypes) {
      var feeHead = buildFeeHead(feeMaster, student, feeGroupFeeType, orgSlug);
      if (!checkIfAlreadyExists(student, feeMaster, feeHead.getFeeType())) {
        feeHeadRepository.save(feeHead);
      }
    }
  }

  private RuleDto.RuleParam buildRuleParam(
      FeeDto.FeeMasterRequest request, FeeMaster feeMaster, String orgSlug) {
    return RuleDto.RuleParam.builder()
        .feeMasterId(feeMaster.getId())
        .orgSlug(orgSlug)
        .paramType(getRuleParamType(request.scopeType()))
        .paramValues(getRuleParamValues(request))
        .build();
  }

  private RuleParamType getRuleParamType(ScopeType scopeType) {
    return switch (scopeType) {
      case SECTION -> RuleParamType.SECTION;
      case GRADE -> RuleParamType.GRADE;
      case SCHOOL -> RuleParamType.SCHOOL;
      case CUSTOM -> RuleParamType.STUDENT_ID;
    };
  }

  private List<String> getRuleParamValues(FeeDto.FeeMasterRequest request) {
    return switch (request.scopeType()) {
      case SECTION -> request.sectionUuid();
      case GRADE -> request.gradeSlug();
      case SCHOOL -> Collections.singletonList("school");
      case CUSTOM -> request.studentId().stream().map(String::valueOf).toList();
    };
  }

  private boolean checkIfAlreadyExists(Student student, FeeMaster feeMaster, FeeType feeType) {
    return feeHeadRepository.existsByFeeMasterAndStudentAndFeeType(feeMaster, student, feeType);
  }

  private void saveFeeMasterStudents(FeeMaster feeMaster, List<Student> students) {
    students.forEach(
        student ->
            feeMasterStudentRepository.save(
                FeeMasterStudent.builder().feeMaster(feeMaster).student(student).build()));
  }

  private FeeHead buildFeeHead(
      FeeMaster feeMaster, Student student, FeeGroupFeeType feeGroupFeeType, String orgSlug) {
    return FeeHead.builder()
        .feeMaster(feeMaster)
        .orgSlug(orgSlug)
        .student(student)
        .amount(feeGroupFeeType.getAmount())
        .balanceAmount(feeGroupFeeType.getAmount())
        .discountAmount(0.0)
        .fineAmount(feeGroupFeeType.getFineAmount())
        .feeType(feeGroupFeeType.getFeeType())
        .dueDate(feeGroupFeeType.getDueDate())
        .status(FeeStatus.UNPAID)
        .build();
  }

  public List<FeeDto.StudentsFeeHeadResponse> getFeeHeadsByStudent(
      String orgSlug, String authUserId) {
    var user = validationUtils.isValidUser(authUserId);
    var feeHeads =
        feeHeadRepository.findAllByStudentIdAndOrgSlugAndStatusNotIn(
            user.getStudentInfo().getId(), orgSlug, List.of(FeeStatus.VOIDED));
    if (feeHeads.isEmpty()) {
      return Collections.emptyList();
    }

    return feeHeads.stream()
        .map(
            feeHead -> {
              var student = feeHead.getStudent();
              var section = student.getSection();
              var feeGroupFeeType =
                  feeGroupFeeTypeRepository.findByFeeGroupIdAndOrgSlug(
                      feeHead.getFeeMaster().getFeeGroup().getId(), feeHead.getOrgSlug());
              var feeHeadFeeType =
                  feeGroupFeeType.stream()
                      .filter(x -> x.getFeeType().equals(feeHead.getFeeType()))
                      .findFirst()
                      .orElseThrow(
                          () ->
                              new IllegalArgumentException(
                                  "Fee type not found for fee head: " + feeHead.getId()));

              var fineAmount = calculateFineAmount(feeHead, feeHeadFeeType);

              return FeeDto.StudentsFeeHeadResponse.builder()
                  .feeHeadId(feeHead.getId().toString())
                  .studentId(student.getId())
                  .studentName(user.getFirstName() + " " + user.getLastName())
                  .gradeSlug(section.getGradeSlug())
                  .gradeName(section.getGradeName())
                  .sectionUuid(section.getUuid().toString())
                  .sectionName(section.getName())
                  .feeGroupId(feeHead.getFeeMaster().getFeeGroup().getId().toString())
                  .feeGroupName(feeHead.getFeeMaster().getFeeGroup().getName())
                  .amount(feeHead.getAmount())
                  .fineAmount(fineAmount)
                  .fineType(feeHeadFeeType.getFineType())
                  .discountAmount(feeHead.getDiscountAmount())
                  .paidAmount(feeHead.getPaidAmount())
                  .status(getPaymentStatus(feeHead))
                  .balanceAmount(
                      (feeHead.getAmount() != null ? feeHead.getAmount() : 0.0)
                          - (feeHead.getDiscountAmount() != null && feeHead.getDiscountAmount() > 0
                              ? feeHead.getDiscountAmount()
                              : 0.0)
                          - (feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
                          + (fineAmount))
                  .dueDate(
                      feeHead.getDueDate() == null
                          ? null
                          : convertIso8601ToEpoch(feeHead.getDueDate()))
                  .feeTypeId(feeHead.getFeeType().getId())
                  .feeTypeCode(feeHead.getFeeType().getCode())
                  .feeTypeName(feeHead.getFeeType().getName())
                  .feeTypeDescription(feeHead.getFeeType().getDescription())
                  .feeMasterId(feeHead.getFeeMaster().getId())
                  .createdOn(convertIso8601ToEpoch(feeHead.getCreatedAt().toLocalDateTime()))
                  .build();
            })
        .toList();
  }

  private FeeStatus getPaymentStatus(FeeHead feeHead) {
    double amount = feeHead.getAmount() != null ? feeHead.getAmount() : 0.0;
    double fine = feeHead.getFineAmount() != null ? feeHead.getFineAmount() : 0.0;
    double totalAmount = amount + fine;
    double paidAmount = feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0;
    double balanceAmount =
        feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : totalAmount - paidAmount;

    if (balanceAmount == 0) {
      return FeeStatus.PAID;
    } else if (paidAmount == 0) {
      return FeeStatus.UNPAID;
    } else if (paidAmount < totalAmount) {
      return FeeStatus.PARTIALLY_PAID;
    }

    return FeeStatus.UNPAID;
  }

  private Double calculateFineAmount(FeeHead feeHead, FeeGroupFeeType feeHeadFeeType) {
    return fineTypeEngine.getFineAmount(feeHead, feeHeadFeeType);
  }

  public void saveFeeMasterStudents(String orgSlug, String studentAuthId, String feeMasterId) {
    var user = validationUtils.isValidUser(studentAuthId);

    var feeMaster = feeService.getFeeMasterById(feeMasterId, orgSlug);
    var request =
        FeeDto.FeeMasterRequest.builder()
            .studentId(Collections.singletonList(user.getStudentInfo().getId()))
            .scopeType(ScopeType.CUSTOM)
            .build();
    createFeeHeads(feeMaster.getId(), request, orgSlug);
  }

  public FeeHead saveFeeHead(
      Student student,
      FeeMaster feeMaster,
      FeeType feeType,
      String orgSlug,
      FeeGroup feeGroup,
      Concession concession) {
    var feeGroupFeeType =
        feeGroupFeeTypeRepository.findByFeeGroupIdAndOrgSlug(feeGroup.getId(), orgSlug).stream()
            .filter(f -> f.getFeeType().equals(feeType))
            .findFirst()
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "Fee type not found in fee group"));

    var amount = feeGroupFeeType.getAmount();
    var fineAmount = feeGroupFeeType.getFineAmount();
    var dueDate = feeGroupFeeType.getDueDate();

    FeeHead feeHead = new FeeHead();
    feeHead.setFeeMaster(feeMaster);
    feeHead.setOrgSlug(orgSlug);
    feeHead.setStudent(student);
    feeHead.setAmount(amount);
    feeHead.setBalanceAmount(amount);
    feeHead.setDiscountAmount(getDiscountAmount(concession, feeHead));
    feeHead.setFineAmount(fineAmount);
    feeHead.setFeeType(feeType);
    feeHead.setDueDate(dueDate);
    feeHead.setStatus(FeeStatus.UNPAID);
    feeHead.setConcession(concession);

    return feeHeadRepository.save(feeHead);
  }

  public double getDiscountAmount(Concession concession, FeeHead feeHead) {
    if (concession == null
        || feeHead == null
        || concession.getValue() == null
        || feeHead.getAmount() == null) {
      return 0.0;
    }

    if (ConcessionType.PERCENTAGE.equals(concession.getType())) {
      if (concession.getValue() < 0 || concession.getValue() > 100) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "error.Concession.InvalidPercentage");
      }

      double discount = feeHead.getAmount() * concession.getValue() / 100;
      return Math.round(discount * 100.0) / 100.0;
    }

    return concession.getValue();
  }

  private FeeHead getFeeHeadById(String feeHeadId, String orgSlug) {
    return feeHeadRepository
        .findByIdAndOrgSlug(UUID.fromString(feeHeadId), orgSlug)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "Fee head not found: " + feeHeadId));
  }

  public void voidFeeHead(String orgSlug, String feeHeadId) {
    var feeHeadData = getFeeHeadById(feeHeadId, orgSlug);
    var oldStatus = feeHeadData.getStatus();

    feeHeadData.setStatus(FeeStatus.VOIDED);
    feeHeadData.setDueDate(null);
    feeHeadData.setBalanceAmount(0.0);
    feeHeadData.setConcession(null);
    feeHeadData.setDiscountAmount(0.0);

    feeHeadRepository.save(feeHeadData);

    auditTrailUtils.log(
        String.valueOf(feeHeadData.getId()),
        "Fee Head",
        "Status Update",
        oldStatus.toString(),
        FeeStatus.VOIDED.toString(),
        orgSlug,
        authService.getUserDetails().getId());
  }
}
